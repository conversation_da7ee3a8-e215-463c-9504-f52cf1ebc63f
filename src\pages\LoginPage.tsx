import React, { useState } from "react";
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabaseClient';
import { UserProfile } from '../types';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [debugMode, setDebugMode] = useState(false);

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔄 Starting OTP send process for email:', email);
      console.log('🔧 Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
      console.log('🔧 Supabase Key exists:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);

      // Send OTP to the email
      const { error: signInError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: true, // automatically create new auth.users record
        }
      });

      console.log('📧 OTP send result:', { error: signInError });

      if (signInError) {
        console.error('❌ OTP send error:', signInError);
        throw signInError;
      }

      setIsOtpSent(true);
      setSuccess('OTP sent! Please check your email for the 6-digit code.');
      console.log('✅ OTP sent successfully');
    } catch (err: any) {
      console.error('❌ Failed to send OTP:', err);
      setError(err.message || 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔄 Starting OTP verification for email:', email);
      console.log('🔑 OTP entered:', otp);

      // Verify OTP with Supabase
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'email'
      });

      console.log('📝 Supabase OTP verification response:', { data, error });

      if (error) {
        console.error('❌ OTP verification error:', error);
        throw error;
      }

      if (data?.session) {
        console.log('✅ Session created successfully:', data.session);
        console.log('👤 User data:', data.user);

        // Store user email in localStorage
        localStorage.setItem('userEmail', email);
        localStorage.setItem('auth_token', data.session.access_token);

        // Fetch user details from temp_website2_users table
        await fetchUserDetailsAndRedirect(email);
      } else {
        console.error('❌ No session data received after OTP verification');
        setError('Verification failed. No session created. Please try again.');
      }
    } catch (err: any) {
      console.error('❌ Verification error:', err);
      setError(err.message || 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDetailsAndRedirect = async (userEmail: string) => {
    try {
      console.log('🔍 Fetching user details for email:', userEmail);

      // Fetch user profile from temp_website2_users table
      const { data: userProfile, error: profileError } = await supabase
        .from('temp_website2_users')
        .select('*')
        .eq('email', userEmail)
        .maybeSingle();

      console.log('📊 User profile query result:', { userProfile, profileError });

      if (profileError) {
        console.error('❌ Error fetching user profile:', profileError);
        // If user doesn't exist in temp_website2_users, redirect to signup
        if (profileError.code === 'PGRST116') {
          console.log('👤 User not found in database, redirecting to signup');
          setSuccess('OTP verified! You need to complete your profile first.');
          localStorage.setItem('pendingUserData', JSON.stringify({
            email: userEmail,
          }));
          setTimeout(() => {
            navigate('/signup');
          }, 2000);
          return;
        }
        throw profileError;
      }

      if (userProfile) {
        console.log('✅ User profile found:', userProfile);

        // Store user profile in localStorage
        localStorage.setItem('userProfile', JSON.stringify(userProfile));

        // Determine user role and redirect
        determineRoleAndRedirect(userProfile);
      } else {
        console.log('⚠️ No user profile found, redirecting to signup');
        setSuccess('OTP verified! You need to complete your profile first.');
        localStorage.setItem('pendingUserData', JSON.stringify({
          email: userEmail,
        }));
        setTimeout(() => {
          navigate('/signup');
        }, 2000);
      }
    } catch (error) {
      console.error('❌ Error in fetchUserDetailsAndRedirect:', error);
      setError('Login successful but failed to load user profile. Please try again.');
    }
  };

  // Helper function to determine role and redirect
  const determineRoleAndRedirect = (userProfile: UserProfile) => {
    console.log('determineRoleAndRedirect called with userProfile:', userProfile);
    
    try {
      // Determine user roles based on profile data
      const isBusinessUser = !!userProfile.business_user_id || !!userProfile.is_business_user;
      const isEmployee = !!userProfile.company_id && !userProfile.business_user_id && !userProfile.is_business_user;
      
      // Check if user is admin (you can add your admin logic here)
      // For now, let's assume users with is_business_user = true and no company_id are admins
      const isAdmin = !!userProfile.is_business_user && !userProfile.company_id;
      
      console.log('Role determination:', {
        isBusinessUser,
        isEmployee,
        isAdmin,
        business_user_id: userProfile.business_user_id,
        is_business_user: userProfile.is_business_user,
        company_id: userProfile.company_id
      });

      // Clear any existing role data
      localStorage.removeItem('currentRole');
      localStorage.removeItem('userRoles');
      localStorage.removeItem('selectedCompany');

      // Determine role priority: Admin > Business > Employee
      if (isAdmin) {
        console.log('User is admin, redirecting to admin dashboard');
        localStorage.setItem('currentRole', 'admin');
        localStorage.setItem('userRoles', JSON.stringify({
          isBusinessUser: false,
          isAdmin: true,
          isEmployee: false
        }));
        setSuccess('Login successful! Redirecting to admin dashboard...');
        setTimeout(() => {
          navigate('/admin');
        }, 1000);
        return;
      }

      if (isBusinessUser) {
        console.log('User is business user, redirecting to employer dashboard');
        localStorage.setItem('currentRole', 'business');
        localStorage.setItem('userRoles', JSON.stringify({
          isBusinessUser: true,
          isAdmin: false,
          isEmployee: false
        }));
        setSuccess('Login successful! Redirecting to employer dashboard...');
        setTimeout(() => {
          navigate('/employer/dashboard');
        }, 1000);
        return;
      }

      if (isEmployee) {
        console.log('User is employee, redirecting to employee dashboard');
        localStorage.setItem('currentRole', 'employee');
        localStorage.setItem('userRoles', JSON.stringify({
          isBusinessUser: false,
          isAdmin: false,
          isEmployee: true
        }));
        // Set selected company for employee
        localStorage.setItem('selectedCompany', JSON.stringify({
          companyID: userProfile.company_id,
          company: userProfile.company_name || ''
        }));
        setSuccess('Login successful! Redirecting to employee dashboard...');
        setTimeout(() => {
          navigate('/employee/dashboard');
        }, 1000);
        return;
      }

      // Fallback: no clear role found
      console.log('No clear role found, defaulting to admin');
      localStorage.setItem('currentRole', 'admin');
      localStorage.setItem('userRoles', JSON.stringify({
        isBusinessUser: false,
        isAdmin: false,
        isEmployee: false
      }));
      setSuccess('Login successful! Redirecting to dashboard...');
      setTimeout(() => {
        navigate('/admin');
      }, 1000);

    } catch (error) {
      console.error('Error in determineRoleAndRedirect:', error);
      setError('Login successful but failed to determine user role. Please contact support.');
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row items-stretch justify-center bg-white">
      {/* Left Side: Image */}
      <div className="w-full md:w-[724px] relative h-64 md:h-auto">
        <img 
          className="absolute inset-0 w-full h-full object-contain" 
          src="/Login.png" 
          alt="Login illustration" 
        />
      </div>

      {/* Right Side: Login Form */}
      <div className="flex flex-col items-center justify-center w-full px-4 py-8 bg-white md:w-1/2">
        <div className="w-full max-w-md">
          <img src="/main.png" alt="Growth Pods Logo" className="h-12 mx-auto mb-6" />
          <h2 className="mb-8 text-3xl font-bold text-center">Sign In</h2>

          <form className="space-y-6" onSubmit={isOtpSent ? handleVerifyOtp : handleSendOtp}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email"
                disabled={isOtpSent}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:border-blue-300"
                required
              />
            </div>

            {isOtpSent && (
              <div>
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                  Enter OTP
                </label>
                <input
                  type="text"
                  id="otp"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="Enter OTP"
                  className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:border-blue-300"
                  required
                />
              </div>
            )}

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
            
            {success && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                {success}
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 px-4 bg-teal-700 text-white font-semibold rounded-md shadow hover:bg-teal-800 disabled:opacity-50"
            >
              {loading ? 'Please wait...' : isOtpSent ? 'Verify OTP' : 'Send OTP'}
            </button>

            {isOtpSent && (
              <button
                type="button"
                onClick={() => {
                  setIsOtpSent(false);
                  setOtp('');
                  setError(null);
                }}
                className="w-full py-2 px-4 text-teal-700 border border-teal-700 font-semibold rounded-md shadow hover:bg-teal-50"
              >
                Change Email
              </button>
            )}

            {isOtpSent && (
              <button
                type="button"
                onClick={handleSendOtp}
                disabled={loading}
                className="w-full py-2 px-4 text-teal-700 border border-teal-700 font-semibold rounded-md shadow hover:bg-teal-50 disabled:opacity-50"
              >
                {loading ? 'Sending...' : 'Resend OTP'}
              </button>
            )}

            <p className="text-center text-sm text-gray-700 mt-6">
              Don't have an account?{' '}
              <Link
                to="/signup"
                className="text-teal-600 hover:text-teal-700 font-medium"
              >
                Sign up
              </Link>
            </p>

            {/* Debug Mode Toggle */}
            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => setDebugMode(!debugMode)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                {debugMode ? 'Hide' : 'Show'} Debug Info
              </button>
            </div>

            {/* Debug Information */}
            {debugMode && (
              <div className="mt-4 p-4 bg-gray-100 rounded text-xs">
                <h4 className="font-bold mb-2">Debug Information:</h4>
                <p><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL}</p>
                <p><strong>Supabase Key Set:</strong> {!!import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Yes' : 'No'}</p>
                <p><strong>Current Email:</strong> {email}</p>
                <p><strong>OTP Sent:</strong> {isOtpSent ? 'Yes' : 'No'}</p>
                <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>

                <div className="mt-2">
                  <strong>Test Users:</strong>
                  <ul className="list-disc list-inside ml-2">
                    <li><EMAIL> (Business User)</li>
                    <li><EMAIL> (Admin User)</li>
                  </ul>
                </div>

                <div className="mt-2">
                  <strong>Common Issues:</strong>
                  <ul className="list-disc list-inside ml-2">
                    <li>Check spam folder for OTP email</li>
                    <li>Ensure email exists in temp_website2_users table</li>
                    <li>Verify Supabase configuration</li>
                    <li>Check browser console for errors</li>
                  </ul>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
