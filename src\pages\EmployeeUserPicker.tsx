import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/apiConfig';

const EmployeeUserPicker: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState('');
  const [companyId, setCompanyId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      const cid = localStorage.getItem('selectedCompanyId');
      setCompanyId(cid);
      if (!cid) {
        setUsers([]);
        setError('No company ID found in localStorage.');
        setLoading(false);
        return;
      }
      try {
        const username = import.meta.env.VITE_CLIENT_ID;
        const password = import.meta.env.VITE_CLIENT_SECRET;
        const basicAuth = btoa(`${username}:${password}`);
        const payload = {
          method: 'getUsers',
          companyId: cid.toUpperCase(),
        };
        const response = await api.post('/reports#getUsers', payload, {
          headers: {
            'Authorization': `Basic ${basicAuth}`,
            'Content-Type': 'application/json',
          },
        });
        const data = response.data;
        if (data && data.users && Array.isArray(data.users) && data.users.length > 0) {
          setUsers(data.users);
          setError(null);
        } else {
          setUsers([]);
          setError('No users found for this company.');
        }
      } catch (err: any) {
        setUsers([]);
        setError('Error fetching users from Rollfi API.');
      }
      setLoading(false);
    };
    fetchUsers();
  }, []);

  const handleSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedUser(e.target.value);
    if (e.target.value) {
      localStorage.setItem('selectedEmployeeId', e.target.value);
    }
  };

  const handleGoToDashboard = () => {
    if (selectedUser) {
      navigate('/employee/dashboard');
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="bg-white p-8 rounded shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold mb-4 text-center">Select an Employee</h2>
        {loading ? (
          <div>Loading employees...</div>
        ) : error ? (
          <div className="text-red-500 mb-4">{error}</div>
        ) : (
          <>
            <select
              className="w-full p-3 border rounded mb-4"
              value={selectedUser}
              onChange={handleSelect}
            >
              <option value="">-- Select Employee --</option>
              {users.map(user => (
                <option key={user.userId} value={user.userId}>
                  {user.user || `${user.firstName || ''} ${user.lastName || ''}`} ({user.email})
                </option>
              ))}
            </select>
            <button
              className={`w-full p-3 rounded ${selectedUser ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
              onClick={handleGoToDashboard}
              disabled={!selectedUser}
            >
              Go to Dashboard
            </button>
          </>
        )}
        <div className="text-xs text-gray-400 mt-2">Debug: companyId = {companyId || 'null'}, users found = {users.length}</div>
      </div>
    </div>
  );
};

export default EmployeeUserPicker; 