# Login Troubleshooting Guide

## Overview
This guide helps diagnose and fix login issues in the US Payroll application.

## Common Login Issues and Solutions

### 1. **OTP Email Not Received**

**Symptoms:**
- User enters email and clicks "Send OTP"
- Success message appears but no email is received

**Solutions:**
- Check spam/junk folder
- Verify email address is correct
- Check Supabase email configuration
- Ensure email provider allows emails from Supabase

**Debug Steps:**
1. Open browser console (F12)
2. Look for error messages when sending OTP
3. Check if Supabase URL and keys are configured correctly

### 2. **OTP Verification Fails**

**Symptoms:**
- OTP email received but verification fails
- "Invalid OTP" error message

**Solutions:**
- Ensure OTP is entered correctly (6 digits)
- Check if <PERSON><PERSON> has expired (usually 60 minutes)
- Try requesting a new OTP

**Debug Steps:**
1. Check browser console for verification errors
2. Verify the OTP format and timing

### 3. **User Profile Not Found After Login**

**Symptoms:**
- <PERSON>TP verification succeeds
- Redirected to signup page instead of dashboard
- Message: "You need to complete your profile first"

**Solutions:**
- Check if user exists in `temp_website2_users` table
- Create user profile in database
- Verify email matches exactly

**Debug Steps:**
1. Check browser console for database query errors
2. Verify user exists in Supabase database

### 4. **Role Determination Issues**

**Symptoms:**
- Login succeeds but redirected to wrong dashboard
- Access denied errors

**Solutions:**
- Check user role fields in database
- Verify role determination logic
- Update user profile with correct role information

## Database Setup

### Required Tables
- `temp_website2_users` - Main user profile table
- `auth.users` - Supabase authentication table

### Test Users
You can create test users with these emails:
- `<EMAIL>` (Business User)
- `<EMAIL>` (Admin User)

## Environment Variables Check

Ensure these are set in `.env`:
```
VITE_SUPABASE_URL=https://krojhpaizojsyhecweat.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
```

## Debug Mode

The login page now includes a debug mode:
1. Go to login page
2. Click "Show Debug Info" at the bottom
3. Review configuration and test with provided test emails

## Manual Testing Steps

1. **Test OTP Sending:**
   ```
   Email: <EMAIL>
   Expected: OTP sent message, email received
   ```

2. **Test OTP Verification:**
   ```
   Enter received OTP
   Expected: Successful verification
   ```

3. **Test User Profile Lookup:**
   ```
   Expected: User found in database or redirected to signup
   ```

4. **Test Role Determination:**
   ```
   Expected: Redirected to appropriate dashboard
   ```

## Common Error Messages

### "Failed to send OTP"
- Check Supabase configuration
- Verify email format
- Check network connectivity

### "Invalid OTP"
- Verify OTP is correct
- Check if OTP expired
- Try requesting new OTP

### "No session created"
- Supabase authentication issue
- Check browser console for details

### "Failed to load user profile"
- Database connection issue
- User doesn't exist in temp_website2_users table

## Quick Fixes

### Create Test User Manually
```sql
INSERT INTO temp_website2_users (
  email, 
  first_name, 
  last_name, 
  phone, 
  is_business_user, 
  user_status
) VALUES (
  '<EMAIL>',
  'Your',
  'Name',
  '1234567890',
  true,
  'active'
);
```

### Reset User Session
```javascript
// In browser console
localStorage.clear();
location.reload();
```

## Contact Support

If issues persist:
1. Check browser console for errors
2. Note exact error messages
3. Verify environment configuration
4. Test with provided test users first
