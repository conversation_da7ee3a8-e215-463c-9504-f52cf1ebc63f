import React, { useState } from 'react';
import { supabase } from '../lib/supabaseClient';

const DatabaseDebugger: React.FC = () => {
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkUser = async () => {
    if (!email) {
      setError('Please enter an email');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Check if user exists in temp_website2_users
      const { data: userProfile, error: profileError } = await supabase
        .from('temp_website2_users')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (profileError) {
        setError(`Database error: ${profileError.message}`);
        return;
      }

      setResult({
        userExists: !!userProfile,
        userProfile: userProfile,
        timestamp: new Date().toISOString()
      });

    } catch (err: any) {
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createTestUser = async () => {
    if (!email) {
      setError('Please enter an email');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // First check if user already exists
      const { data: existingUser } = await supabase
        .from('temp_website2_users')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (existingUser) {
        setResult({
          userExists: true,
          message: 'User already exists',
          userData: existingUser,
          timestamp: new Date().toISOString()
        });
        return;
      }

      const testUser = {
        email: email,
        first_name: 'Test',
        last_name: 'User',
        phone: '1234567890',
        is_business_user: true,
        user_status: 'active',
        created_at: new Date().toISOString()
      };

      console.log('Creating user with data:', testUser);

      const { data, error } = await supabase
        .from('temp_website2_users')
        .insert([testUser])
        .select();

      if (error) {
        console.error('Database error:', error);
        setError(`Error creating user: ${error.message}`);
        return;
      }

      console.log('User created successfully:', data);

      setResult({
        userCreated: true,
        userData: data[0],
        timestamp: new Date().toISOString()
      });

    } catch (err: any) {
      console.error('Script error:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const listAllUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('temp_website2_users')
        .select('email, first_name, last_name, is_business_user, user_status')
        .limit(10);

      if (error) {
        setError(`Error fetching users: ${error.message}`);
        return;
      }

      setResult({
        allUsers: data,
        count: data.length,
        timestamp: new Date().toISOString()
      });

    } catch (err: any) {
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Database Debugger</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter email to check"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex space-x-2">
          <button
            onClick={checkUser}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Checking...' : 'Check User'}
          </button>
          
          <button
            onClick={createTestUser}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Test User'}
          </button>
          
          <button
            onClick={listAllUsers}
            disabled={loading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'List All Users'}
          </button>
        </div>

        {error && (
          <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {result && (
          <div className="p-3 bg-gray-100 border border-gray-300 rounded">
            <h4 className="font-semibold mb-2">Result:</h4>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatabaseDebugger;
