<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 3px;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Login System Test</h1>
    
    <div class="test-section">
        <h2>1. Environment Variables Test</h2>
        <div id="env-test">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>2. Supabase Connection Test</h2>
        <div id="supabase-test">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>3. Send OTP Test</h2>
        <input type="email" id="test-email" placeholder="Enter your email" value="<EMAIL>">
        <button onclick="testSendOTP()">Send OTP</button>
        <div id="otp-test"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Database Query Test</h2>
        <button onclick="testDatabaseQuery()">Test Database Connection</button>
        <div id="db-test"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        // Test environment variables
        const supabaseUrl = 'https://krojhpaizojsyhecweat.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtyb2pocGFpem9qc3loZWN3ZWF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg4NzE4MjYsImV4cCI6MjA1NDQ0NzgyNn0.GsDMLMwqwzkRuaguuSFnTlXWy6D_En6GzkJZniVyyYM';
        
        document.getElementById('env-test').innerHTML = supabaseUrl && supabaseKey ? 
            '<div class="success">✓ Environment variables are set</div>' : 
            '<div class="error">✗ Environment variables missing</div>';
        
        // Initialize Supabase client
        let supabase;
        try {
            supabase = createClient(supabaseUrl, supabaseKey);
            document.getElementById('supabase-test').innerHTML = '<div class="success">✓ Supabase client initialized</div>';
        } catch (error) {
            document.getElementById('supabase-test').innerHTML = `<div class="error">✗ Supabase initialization failed: ${error.message}</div>`;
        }
        
        // Test OTP sending
        window.testSendOTP = async function() {
            const email = document.getElementById('test-email').value;
            const resultDiv = document.getElementById('otp-test');
            
            if (!email) {
                resultDiv.innerHTML = '<div class="error">Please enter an email</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = 'Sending OTP...';
                const { error } = await supabase.auth.signInWithOtp({
                    email,
                    options: {
                        shouldCreateUser: true
                    }
                });
                
                if (error) throw error;
                
                resultDiv.innerHTML = '<div class="success">✓ OTP sent successfully! Check your email.</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ OTP sending failed: ${error.message}</div>`;
            }
        };
        
        // Test database query
        window.testDatabaseQuery = async function() {
            const resultDiv = document.getElementById('db-test');
            
            try {
                resultDiv.innerHTML = 'Testing database connection...';
                
                // Test if we can query the temp_website2_users table
                const { data, error } = await supabase
                    .from('temp_website2_users')
                    .select('count(*)')
                    .limit(1);
                
                if (error) throw error;
                
                resultDiv.innerHTML = '<div class="success">✓ Database connection successful</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Database query failed: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
