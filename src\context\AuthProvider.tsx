import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabaseClient';
import { Session, User } from '@supabase/supabase-js';
import { UserProfile } from '../types';

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  userProfile: UserProfile | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Supabase auth event: ${event}`);
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        if (session?.user) {
          try {
            // Set the auth token from Supabase session
            if (session.access_token) {
              localStorage.setItem('auth_token', session.access_token);
              console.log('Auth token set from Supabase session');
            }

            // Check if user profile is already in localStorage
            const storedProfile = localStorage.getItem('userProfile');
            if (storedProfile) {
              try {
                const parsedProfile = JSON.parse(storedProfile);
                setUserProfile(parsedProfile);
                console.log('[AuthProvider] User profile loaded from localStorage:', parsedProfile);
                return;
              } catch (parseError) {
                console.error('[AuthProvider] Error parsing stored profile:', parseError);
                localStorage.removeItem('userProfile');
              }
            }

            // Fetch user profile from database
            console.log('[AuthProvider] 🔍 Fetching user profile for email:', session.user.email);

            try {
              const { data, error } = await supabase
                .from('temp_website2_users')
                .select('*')
                .eq('email', session.user.email)
                .maybeSingle();

              console.log('[AuthProvider] 📊 User profile query result:', { data, error });

              if (error) {
                console.error('[AuthProvider] ❌ Error fetching user profile:', error);
                // If the error is 'No rows found', the user needs to complete their profile
                if (error.code === 'PGRST116') {
                  console.log('[AuthProvider] 👤 User not found in database, setting profile to null');
                  setUserProfile(null);
                  // Only redirect if not already on signup page
                  if (!window.location.pathname.includes('/signup') && !window.location.pathname.includes('/login')) {
                    console.log('[AuthProvider] 🔄 Redirecting to signup page');
                    navigate('/signup');
                  }
                } else {
                  console.error('[AuthProvider] Database error:', error);
                  setUserProfile(null);
                }
              } else if (data) {
                console.log('[AuthProvider] ✅ User profile found:', data);
                setUserProfile(data);
                localStorage.setItem('userProfile', JSON.stringify(data));
                console.log('[AuthProvider] 💾 User profile saved to localStorage');
              } else {
                console.log('[AuthProvider] ⚠️ No user profile data returned');
                setUserProfile(null);
                // Only redirect if not already on signup page
                if (!window.location.pathname.includes('/signup') && !window.location.pathname.includes('/login')) {
                  console.log('[AuthProvider] 🔄 Redirecting to signup page (no data)');
                  navigate('/signup');
                }
              }
            } catch (dbError) {
              console.error('[AuthProvider] Database query failed:', dbError);
              setUserProfile(null);
            }
          } catch (error) {
            console.error('[AuthProvider] Error in auth state change:', error);
            setUserProfile(null);
          }
        } else {
          setUserProfile(null);
          // Clear auth token and user profile when user logs out
          localStorage.removeItem('auth_token');
          localStorage.removeItem('userProfile');
          localStorage.removeItem('userEmail');
          localStorage.removeItem('currentRole');
          localStorage.removeItem('userRoles');
          localStorage.removeItem('selectedCompany');
          
          // If user is not logged in and trying to access protected routes, redirect to login
          const currentPath = window.location.pathname;
          if (currentPath !== '/login' && currentPath !== '/signup' && !currentPath.startsWith('/auth/')) {
            navigate('/login');
          }
        }
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [navigate]);

  const value = {
    session,
    user,
    loading,
    userProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export { AuthProvider as default, useAuth };