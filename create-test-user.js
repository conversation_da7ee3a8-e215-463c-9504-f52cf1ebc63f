// Test user creation script
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://krojhpaizojsyhecweat.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtyb2pocGFpem9qc3loZWN3ZWF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg4NzE4MjYsImV4cCI6MjA1NDQ0NzgyNn0.GsDMLMwqwzkRuaguuSFnTlXWy6D_En6GzkJZniVyyYM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTestUser() {
  try {
    console.log('Creating test user...');
    
    // Test user data
    const testUser = {
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      phone: '1234567890',
      is_business_user: true,
      user_status: 'active',
      created_at: new Date().toISOString()
    };
    
    // Insert test user into temp_website2_users table
    const { data, error } = await supabase
      .from('temp_website2_users')
      .insert([testUser])
      .select();
    
    if (error) {
      console.error('Error creating test user:', error);
      return;
    }
    
    console.log('Test user created successfully:', data);
    
    // Also create an admin user
    const adminUser = {
      email: '<EMAIL>',
      first_name: 'Admin',
      last_name: 'User',
      phone: '9876543210',
      is_business_user: true,
      user_status: 'active',
      created_at: new Date().toISOString()
    };
    
    const { data: adminData, error: adminError } = await supabase
      .from('temp_website2_users')
      .insert([adminUser])
      .select();
    
    if (adminError) {
      console.error('Error creating admin user:', adminError);
      return;
    }
    
    console.log('Admin user created successfully:', adminData);
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

// Run the script
createTestUser();
