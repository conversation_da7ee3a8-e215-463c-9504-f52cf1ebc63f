import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../services/apiConfig';

const ProcessingPayrollPage: React.FC = () => {
  const { payPeriodId } = useParams();
  const [details, setDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDetails = async () => {
      setLoading(true);
      setError(null);
      if (!payPeriodId) {
        setError('No pay period ID provided.');
        setLoading(false);
        return;
      }
      try {
        const username = import.meta.env.VITE_CLIENT_ID;
        const password = import.meta.env.VITE_CLIENT_SECRET;
        const basicAuth = btoa(`${username}:${password}`);
        const payload = {
          method: 'getPayPeriodDetails',
          payPeriodId: payPeriodId.toUpperCase(),
        };
        const response = await api.post('/reports#getPayPeriodDetails', payload, {
          headers: {
            'Authorization': `Basic ${basicAuth}`,
            'Content-Type': 'application/json',
          },
        });
        setDetails(response.data);
      } catch (err: any) {
        setError('Error fetching pay period details.');
      }
      setLoading(false);
    };
    fetchDetails();
  }, [payPeriodId]);

  const handleSubmitPayroll = async () => {
    if (!payPeriodId) return;
    setSubmitting(true);
    setSubmitResult(null);
    try {
      const companyId = localStorage.getItem('selectedCompanyId');
      if (!companyId) throw new Error('No company ID found.');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'initiatePayroll',
        companyId: companyId.toUpperCase(),
        payPeriodId: payPeriodId.toUpperCase(),
      };
      const response = await api.post('/payroll#initiatePayroll', payload, {
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json',
        },
      });
      setSubmitResult('Payroll submitted successfully!');
    } catch (err: any) {
      setSubmitResult('Error submitting payroll.');
    }
    setSubmitting(false);
  };

  if (loading) return <div className="p-8">Loading payroll details...</div>;
  if (error) return <div className="p-8 text-red-500">{error}</div>;
  if (!details) return <div className="p-8">No details found.</div>;

  // Example structure, adjust fields as needed based on API response
  const { payPeriod, debitAccount, debitAmount, payDate, payDeadline, totalCashRequired, totalEmployerTaxes, totalDeductions, employees = [] } = details;

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Confirm payroll details</h2>
        <button
          className="bg-teal-600 text-white px-6 py-2 rounded hover:bg-teal-700"
          onClick={handleSubmitPayroll}
          disabled={submitting}
        >
          {submitting ? 'Submitting...' : 'Submit payroll'}
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div>
          <div className="text-gray-500 text-sm">Pay Period</div>
          <div className="font-semibold">{payPeriod}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Debit account</div>
          <div className="font-semibold">{debitAccount}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Debit amount</div>
          <div className="font-semibold">{debitAmount}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Pay date</div>
          <div className="font-semibold">{payDate}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Pay deadline</div>
          <div className="font-semibold">{payDeadline}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Total cash required</div>
          <div className="font-semibold">{totalCashRequired}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Total employer taxes</div>
          <div className="font-semibold">{totalEmployerTaxes}</div>
        </div>
        <div>
          <div className="text-gray-500 text-sm">Total deductions</div>
          <div className="font-semibold">{totalDeductions}</div>
        </div>
      </div>
      <div className="bg-white rounded shadow p-4">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="text-left py-2">Name</th>
              <th className="text-right py-2">Gross total</th>
              <th className="text-right py-2">Employer tax</th>
              <th className="text-right py-2">Sub total</th>
              <th className="text-left py-2">Payment method</th>
            </tr>
          </thead>
          <tbody>
            {employees.map((emp: any, idx: number) => (
              <tr key={idx}>
                <td className="py-2 font-semibold">{emp.name}</td>
                <td className="text-right">{emp.grossTotal}</td>
                <td className="text-right">{emp.employerTax}</td>
                <td className="text-right">{emp.subTotal}</td>
                <td>{emp.paymentMethod}</td>
              </tr>
            ))}
            <tr className="font-bold">
              <td className="py-2">Total</td>
              <td className="text-right">{employees.reduce((sum: number, emp: any) => sum + (parseFloat(emp.grossTotal) || 0), 0).toLocaleString(undefined, { style: 'currency', currency: 'USD' })}</td>
              <td className="text-right">{employees.reduce((sum: number, emp: any) => sum + (parseFloat(emp.employerTax) || 0), 0).toLocaleString(undefined, { style: 'currency', currency: 'USD' })}</td>
              <td className="text-right">{employees.reduce((sum: number, emp: any) => sum + (parseFloat(emp.subTotal) || 0), 0).toLocaleString(undefined, { style: 'currency', currency: 'USD' })}</td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>
      {submitResult && <div className="mt-4 text-center font-semibold text-teal-700">{submitResult}</div>}
    </div>
  );
};

export default ProcessingPayrollPage; 